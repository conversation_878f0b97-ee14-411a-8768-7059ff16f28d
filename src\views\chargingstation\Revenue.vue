<template>
  <el-row :gutter="24">
    <el-col :span="4">
      <el-card>
        <div class="title">
          <div class="background">
            <el-icon>
              <Document />
            </el-icon>
          </div>
          <h3 class="title ml">今日总收入（元）</h3>
        </div>
        <div class="total mt">
          <h1>{{ TOthousands(12239824) }}</h1>
          <div class="percent">-21%</div>
        </div>
      </el-card>
    </el-col>
    <el-col :span="4">
      <el-card>
        <div class="title">
          <div class="background">
            <el-icon>
              <Document />
            </el-icon>
          </div>
          <h3 class="title ml">本月总收入（万元）</h3>
        </div>
        <div class="total mt">
          <h1>{{ TOthousands(2924) }}</h1>
          <div class="percent">-21%</div>
        </div>
      </el-card>
    </el-col>
    <el-col :span="4">
      <el-card>
        <div class="title">
          <div class="background">
            <el-icon>
              <Document />
            </el-icon>
          </div>
          <h3 class="title ml">会员卡储值金额（元）</h3>
        </div>
        <div class="total mt">
          <h1>{{ TOthousands(239824) }}</h1>
          <div class="percent">-16%</div>
        </div>
      </el-card>
    </el-col>
    <el-col :span="4">
      <el-card>
        <div class="title">
          <div class="background">
            <el-icon>
              <Document />
            </el-icon>
          </div>
          <h3 class="title ml">服务费总金额（元）</h3>
        </div>
        <div class="total mt">
          <h1>{{ TOthousands(16824) }}</h1>
          <div class="percent">-7%</div>
        </div>
      </el-card>
    </el-col>
    <el-col :span="4">
      <el-card>
        <div class="title">
          <div class="background">
            <el-icon>
              <Document />
            </el-icon>
          </div>
          <h3 class="title ml">停车费总金额（元）</h3>
        </div>
        <div class="total mt">
          <h1>{{ TOthousands(9687) }}</h1>
          <div class="percent">-4%</div>
        </div>
      </el-card>
    </el-col>
    <el-col :span="4">
      <el-card>
        <div class="title">
          <div class="background">
            <el-icon>
              <Document />
            </el-icon>
          </div>
          <h3 class="title ml">电费总金额（元）</h3>
        </div>
        <div class="total mt">
          <h1>{{ TOthousands(223674) }}</h1>
          <div class="percent">-19%</div>
        </div>
      </el-card>
    </el-col>
  </el-row>

  <el-card class="mt">
    <div ref="myChart" style="width: 100%;height: 400px;"></div>
  </el-card>

</template>

<script setup lang="ts">
import TOthousands from "@/utils/toThousands"
import { useChart } from "@/hook/useChart"
import { reactive, ref } from "vue"
import { getRevenueChart } from "@/api/chargingstation/tabledata"

const myChart = ref(null)
const getChartData = async () => {
  const option = reactive({
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: [], // 图例数据，会自动从series的name中获取
      top: 'top', // 图例位置
      left: 'center'
    },
    xAxis: {
      type: 'category',
      data: ['一月', '二月', '三月', '四月', '五月', '六月', '七月']
    },
    yAxis: [
      {
        type: 'value',
        name: '销售',
        position: 'left'
      },
      {
        type: 'value',
        name: '访问量',
        position: 'right'
      }
    ],
    series: [
      {
        name: '',
        data: [],
        type: 'bar',
        yAxisIndex: 0,
      },
      {
        name: '',
        data: [],
        type: 'line',
        yAxisIndex: 1,
        smooth: true
      }
    ]
  });

    const res = await getRevenueChart()
    console.log('Revenue chart data:', res)
    // 更新图例数据
    option.legend.data = res.data.list.map((item: any) => item.name)
    // 更新系列数据
    for(let i=0;i<res.data.list.length;i++){
        option.series[i].name = res.data.list[i].name
        option.series[i].data = res.data.list[i].data
    }
    return option
}
useChart(myChart, getChartData())

</script>

<style scoped lang="less">
.title {
  display: flex;
  align-items: center;

  .background {
    width: 30px;
    height: 30px;
    background-color: #ebecf5;
    border-radius: 50%;
    text-align: center;
    line-height: 30px;
  }
}

.total {
  display: flex;
  align-items: center;

  h1 {
    font-size: 32px;
  }

  .percent {
    margin-left: 10px;
    background-color: rgb(235, 247, 239);
    color: green;
  }
}
</style>
