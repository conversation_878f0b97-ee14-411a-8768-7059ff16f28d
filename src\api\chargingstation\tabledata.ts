import {post} from "@/utils/http"

enum Api{
    TableData="/stationList",
    Edit="/station/edit",
    Delete="/station/delete",
    Chart="/revenueChart"

}

function getTableData(data:any){
    return post(Api.TableData,data)
}

function editStation(data:any){
    return post(Api.Edit,data)
}

function deleteStation(data:any){
    return post(Api.Delete,data)
}



function getRevenueChart(){
    return post(Api.Chart)
}


export {getTableData,editStation,deleteStation,getRevenueChart}